package com.ymx.photovoltaic.ui.page.equipment

import androidx.compose.foundation.Image
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonTitleField
import com.ymx.photovoltaic.ui.page.widget.ConfirmButton
import com.ymx.photovoltaic.ui.page.widget.LoadingDialog
import com.ymx.photovoltaic.ui.page.widget.PlaceholderText
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.ui.page.widget.rememberSingleColumnPickerState
import com.ymx.photovoltaic.util.DateTimeUtil
import com.ymx.photovoltaic.util.KtorSocketUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.execute_operation
import photovoltaic_kmp_app.composeapp.generated.resources.manual_networking
import photovoltaic_kmp_app.composeapp.generated.resources.network_result_query
import photovoltaic_kmp_app.composeapp.generated.resources.operate_relay
import photovoltaic_kmp_app.composeapp.generated.resources.operate_type
import photovoltaic_kmp_app.composeapp.generated.resources.optimizer_burn
import photovoltaic_kmp_app.composeapp.generated.resources.optimizer_param_query
import photovoltaic_kmp_app.composeapp.generated.resources.please_select
import photovoltaic_kmp_app.composeapp.generated.resources.query_window
import photovoltaic_kmp_app.composeapp.generated.resources.query_window_placeholder
import photovoltaic_kmp_app.composeapp.generated.resources.relay_settings
import photovoltaic_kmp_app.composeapp.generated.resources.right_arrow
import photovoltaic_kmp_app.composeapp.generated.resources.select_operation
import photovoltaic_kmp_app.composeapp.generated.resources.select_relay
import photovoltaic_kmp_app.composeapp.generated.resources.sending

@Composable
fun SetRelayPage(
    navHostController: NavHostController,
    imei: String
) {
    var operateInfo by remember { mutableStateOf("") }
    
    // 添加中继器选择状态
    val relayPicker = rememberSingleColumnPickerState()
    var relayValue by remember { mutableIntStateOf(0) }
    var relayText by remember { mutableStateOf("") }
    var relayError by remember { mutableStateOf<String?>(null) }
    
    // 添加操作类型选择状态
    val operationPicker = rememberSingleColumnPickerState()
    var operationValue by remember { mutableIntStateOf(0) }
    var operationText by remember { mutableStateOf("") }
    var operationError by remember { mutableStateOf<String?>(null) }
    
    // 使用 AppGlobal 中的数据
    val relayList = remember { AppGlobal.relayList }
    val relayChipIdMap = remember { AppGlobal.relayChipIdMap }
    
    // 中继器选项列表 - 使用从 AppGlobal 中获取的数据
    val relayRange = remember(relayList) {
        relayList.map { it.relayId }
    }
    
    // 先获取操作类型字符串资源
    val optimizerBurn = stringResource(Res.string.optimizer_burn)
    val manualNetworking = stringResource(Res.string.manual_networking)
    val networkResultQuery = stringResource(Res.string.network_result_query)
    val optimizerParamQuery = stringResource(Res.string.optimizer_param_query)
    
    // 操作类型选项列表
    val operationRange = remember {
        listOf(
            optimizerBurn,
            manualNetworking,
            networkResultQuery,
            optimizerParamQuery
        )
    }

    // 监听操作类型和中继器选择的变化，并更新显示的文本
    LaunchedEffect(operationText, relayText) {
        if (operationText == optimizerBurn && relayText.isNotEmpty()) {
            // 从本地数据中获取该中继器下的优化器列表
            val groupChipIdMap = relayChipIdMap[relayText] ?: emptyMap()
            // 将所有组的 chipId 合并成一个列表
            val chipIdList = groupChipIdMap.values.flatten()

            // 更新显示的文本
            val header = "中继器ID: $relayText 服务器端已录入的优化器id有${chipIdList.size}个:\n"

            // 将优化器列表分组，每组3个
            val groupedOptimizers = chipIdList.chunked(3)

            val optimizerContent = groupedOptimizers.mapIndexed { groupIndex, group ->
                val lineContent = group.mapIndexed { index, chipId ->
                    "${groupIndex * 3 + index + 1}.$chipId"
                }.joinToString("    ")

                "$lineContent\n-------------------------------------------"
            }.joinToString("\n")
            
            // 添加时间戳并追加到现有结果
            val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
            val newInfo = "[$timestamp] 查询优化器列表:\n$header$optimizerContent"
            
            // 如果已有内容，则添加分隔线
            operateInfo = if (operateInfo.isNotEmpty()) {
                "$operateInfo\n\n$newInfo"
            } else {
                newInfo
            }
        }
    }

    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.relay_settings), backClick = { navHostController.popBackStack() })
        },
        containerColor = Grey_F5
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(start = 10.dp, end = 10.dp, top = 10.dp, bottom = 20.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                var showLoadingDialog by remember { mutableStateOf(false) }
                if (showLoadingDialog) {
                    LoadingDialog(loadingText = stringResource(Res.string.sending)) { showLoadingDialog = false }
                }

                // 查询中继器的UI
                    val scrollState = rememberScrollState()
                    
                    // 监听 operateInfo 的变化，自动滚动到底部
                    LaunchedEffect(operateInfo) {
                        scrollState.animateScrollTo(scrollState.maxValue)
                    }
                    
                    CommonTitleField(
                        value = operateInfo,
                        onValueChange = { },
                        titleText = stringResource(Res.string.query_window),
                        placeholderCom = {
                            PlaceholderText(Res.string.query_window_placeholder)
                        },
                        modifier = Modifier
                            .padding(top = 5.dp),
                        isReadOnly = true,
                        textFieldHeight = 300,
                        cornerRadius = 20,
                        isSingleLine = false,
                        interactionSource = remember { MutableInteractionSource() },
                        scrollState= scrollState

                    )

                // 添加中继器选择
                val selectRelay = stringResource(Res.string.select_relay)
                CommonTitleField(
                    value = relayText,
                    onValueChange = { },
                    titleText = stringResource(Res.string.operate_relay),
                    isSelected = relayText.isNotEmpty(),
                    placeholderCom = {
                        PlaceholderText(Res.string.please_select)
                    },
                    modifier = Modifier.padding(top = 5.dp),
                    textFieldHeight = 48,
                    cornerRadius = 30,
                    titleBottom = 10,
                    textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                    isReadOnly = true,
                    isError = relayError != null,
                    errorText = relayError ?: "",
                    onBoxClick = {
                        if (relayRange.isNotEmpty()) {
                            relayPicker.show(
                                title = selectRelay,
                                range = relayRange,
                                value = relayValue
                            ) {
                                relayValue = it
                                relayText = if (it >= 0 && it < relayRange.size) relayRange[it] else ""
                                relayError = null
                            }
                        } else {
                            relayError = "没有可用的中继器"
                        }
                    },
                    trailingIconCom = {
                        Image(
                            painter = painterResource(Res.drawable.right_arrow),
                            modifier = Modifier.size(18.dp),
                            contentDescription = "Select Relay"
                        )
                    }
                )

                // 添加操作类型选择
                val selectOperation = stringResource(Res.string.select_operation)
                CommonTitleField(
                    value = operationText,
                    onValueChange = { },
                    titleText = stringResource(Res.string.operate_type),
                    isSelected = operationText.isNotEmpty(),
                    placeholderCom = {
                        PlaceholderText(Res.string.please_select)
                    },
                    modifier = Modifier.padding(top = 5.dp),
                    textFieldHeight = 48,
                    cornerRadius = 30,
                    titleBottom = 10,
                    textStyle = TextStyle(fontSize = 14.sp, lineHeight = 14.sp),
                    isReadOnly = true,
                    isError = operationError != null,
                    errorText = operationError ?: "",
                    onBoxClick = {
                        if (operationRange.isNotEmpty()) {
                            operationPicker.show(
                                title = selectOperation,
                                range = operationRange,
                                value = operationValue
                            ) {
                                operationValue = it
                                operationText = if (it >= 0 && it < operationRange.size) operationRange[it] else ""
                                operationError = null
                            }
                        } else {
                            operationError = "没有可用的操作类型"
                        }
                    },
                    trailingIconCom = {
                        Image(
                            painter = painterResource(Res.drawable.right_arrow),
                            modifier = Modifier.size(18.dp),
                            contentDescription = "Select Operation"
                        )
                    }
                )

                Spacer(modifier = Modifier.height(50.dp))

                // 将查询按钮改为执行按钮
                ConfirmButton(stringResource(Res.string.execute_operation), true) {
                    if (relayText.isEmpty() || operationText.isEmpty()) {
                        // 添加时间戳并追加到现有结果
                        val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
                        val newInfo = "[$timestamp] 错误: 请选择中继器和操作类型"
                        
                        // 如果已有内容，则添加分隔线
                        operateInfo = if (operateInfo.isNotEmpty()) {
                            "$operateInfo\n$newInfo"
                        } else {
                            newInfo
                        }
                        return@ConfirmButton
                    }
                    
                    showLoadingDialog = true
                    
                    // 根据不同的操作类型构造不同的byteArray
                    when (operationText) {
                        optimizerBurn -> {
                            // 优化器烧录 - 根据 powerStationType 区别处理
                            val groupChipIdMap = relayChipIdMap[relayText] ?: emptyMap()

                            when (AppGlobal.powerStationType) {
                                1 -> {
                                    // powerStationType = 1: 保持现有调用方法不变
                                    val chipIdList = groupChipIdMap.values.flatten()
                                    executeOptimizerBurn(relayText, chipIdList, 0, 0) { status ->
                                        showLoadingDialog = false

                                        // 添加时间戳并追加到现有结果
                                        val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
                                        val newInfo = "[$timestamp] \n$status"

                                        // 如果已有内容，则添加分隔线
                                        operateInfo = if (operateInfo.isNotEmpty()) {
                                            "$operateInfo\n$newInfo"
                                        } else {
                                            newInfo
                                        }
                                    }
                                }
                                2 -> {
                                    // powerStationType = 2: 循环每个组串分别调用
                                    val groupList = groupChipIdMap.toList()
                                    var currentGroupIndex = 0
                                    var hasError = false

                                    fun processNextGroup() {
                                        if (currentGroupIndex >= groupList.size || hasError) {
                                            showLoadingDialog = false
                                            return
                                        }

                                        val (groupId, chipIdList) = groupList[currentGroupIndex]
                                        val isLastGroup = currentGroupIndex == groupList.size - 1
                                        val moreChipid = if (isLastGroup) 0 else 1

                                        // 根据组串id格式设置 chipidSeqNo
                                        val chipidSeqNo = if (groupId.contains("-")) {
                                            // 如果包含 "-"，获取 "-" 之后的值
                                            val afterDash = groupId.substringAfterLast("-")
                                            afterDash.toIntOrNull() ?: (groupId.hashCode() and 0xFF)
                                        } else {
                                            // 如果不包含 "-"，使用 hashCode
                                            groupId.hashCode() and 0xFF
                                        }

                                        executeOptimizerBurn(relayText, chipIdList, chipidSeqNo, moreChipid) { status ->
                                            // 检查是否返回失败字符串
                                            if (status.contains("失败")) {
                                                hasError = true
                                                showLoadingDialog = false

                                                // 添加时间戳并追加到现有结果
                                                val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
                                                val newInfo = "[$timestamp] \n$status"

                                                operateInfo = if (operateInfo.isNotEmpty()) {
                                                    "$operateInfo\n$newInfo"
                                                } else {
                                                    newInfo
                                                }
                                                return@executeOptimizerBurn
                                            }

                                            // 成功的情况
                                            if (isLastGroup) {
                                                // 最后一个组，显示成功字符串
                                                showLoadingDialog = false

                                                val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
                                                val newInfo = "[$timestamp] \n$status"

                                                operateInfo = if (operateInfo.isNotEmpty()) {
                                                    "$operateInfo\n$newInfo"
                                                } else {
                                                    newInfo
                                                }
                                            } else {
                                                // 不是最后一个组，继续下一次调用
                                                currentGroupIndex++
                                                processNextGroup()
                                            }
                                        }
                                    }

                                    // 开始处理第一个组
                                    processNextGroup()
                                }
                                else -> {
                                    // 默认情况，使用 powerStationType = 1 的逻辑
                                    val chipIdList = groupChipIdMap.values.flatten()
                                    executeOptimizerBurn(relayText, chipIdList, 0, 0) { status ->
                                        showLoadingDialog = false

                                        val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
                                        val newInfo = "[$timestamp] \n$status"

                                        operateInfo = if (operateInfo.isNotEmpty()) {
                                            "$operateInfo\n$newInfo"
                                        } else {
                                            newInfo
                                        }
                                    }
                                }
                            }
                        }
                        manualNetworking -> {
                            // 手动组网
                            executeManualNetworking(relayText) { status ->
                                showLoadingDialog = false
                                
                                // 添加时间戳并追加到现有结果
                                val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
                                val newInfo = "[$timestamp] \n$status"
                                
                                // 如果已有内容，则添加分隔线
                                operateInfo = if (operateInfo.isNotEmpty()) {
                                    "$operateInfo\n$newInfo"
                                } else {
                                    newInfo
                                }
                            }
                        }
                        networkResultQuery -> {
                            // 组网结果查询
                            executeNetworkResultQuery(relayText) { status ->
                                showLoadingDialog = false
                                
                                // 添加时间戳并追加到现有结果
                                val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
                                val newInfo = "[$timestamp] \n$status"
                                
                                // 如果已有内容，则添加分隔线
                                operateInfo = if (operateInfo.isNotEmpty()) {
                                    "$operateInfo\n$newInfo"
                                } else {
                                    newInfo
                                }
                            }
                        }
                        optimizerParamQuery -> {
                            // 优化器参数查询
                            executeOptimizerParamQuery(relayText) { status ->
                                showLoadingDialog = false
                                
                                // 添加时间戳并追加到现有结果
                                val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
                                val newInfo = "[$timestamp] \n$status"
                                
                                // 如果已有内容，则添加分隔线
                                operateInfo = if (operateInfo.isNotEmpty()) {
                                    "$operateInfo\n$newInfo"
                                } else {
                                    newInfo
                                }
                            }
                        }
                        else -> {
                            showLoadingDialog = false
                            
                            // 添加时间戳并追加到现有结果
                            val timestamp = DateTimeUtil.getFormattedTimestampWithBrackets()
                            val newInfo = "[$timestamp] 错误: 未知操作类型"
                            
                            // 如果已有内容，则添加分隔线
                            operateInfo = if (operateInfo.isNotEmpty()) {
                                "$operateInfo\n$newInfo"
                            } else {
                                newInfo
                            }
                        }
                    }
                }
            }
        }
    }
}

//0x01 中继器组网结果查询
//0x02 优化器电气参数查询
//0x03 单个优化器ID替换
//0x04 优化器ID烧录
//0x05 手动触发中继器组网

// 组网结果查询 0x01
fun executeNetworkResultQuery(relayId: String, callback: (String) -> Unit) {
    // cmd(1) + relay_id(5)+ optimizer_seq_no(1)
    // optimizer_seq_no 标识已从中继器请求获取的优化器ID数量，初始值为0
    val byteArray = ByteArray(7)
    // cmd
    byteArray[0] = 0x01.toByte()

    // relay_id
    KtorSocketUtils.hexStringToBytes(relayId)?.copyInto(byteArray, 1, 0, 5)
    byteArray[6] = 0

    KtorSocketUtils.sendSocketCommand(byteArray) { result ->
        when (result) {
            is KtorSocketUtils.SocketResult.Success -> callback("组网结果查询：\n${result.response}")
            is KtorSocketUtils.SocketResult.Error -> {
                CoroutineScope(Dispatchers.Main).launch {
                    callback("组网结果查询失败：${result.message}")
                }
            }
        }
    }
}

// 优化器电气信息缓存值查询 0x02
fun executeOptimizerParamQuery(relayId: String, callback: (String) -> Unit) {

    // cmd(1) + relay_id(5)+ optimizer_seq_no(1)
    // optimizer_seq_no 标识已从中继器请求获取的优化器ID数量，初始值为0

    val byteArray = ByteArray(7)
    // cmd
    byteArray[0] = 0x02.toByte()

    // relay_id
    KtorSocketUtils.hexStringToBytes(relayId)?.copyInto(byteArray, 1, 0, 5)
    byteArray[6] = 0

    KtorSocketUtils.sendSocketCommand(byteArray) { result ->
        when (result) {
            is KtorSocketUtils.SocketResult.Success -> callback("优化器电气参数缓存值查询：\n${result.response}")
            is KtorSocketUtils.SocketResult.Error -> {
                CoroutineScope(Dispatchers.Main).launch {
                    callback("优化器电气参数缓存值查询失败：${result.message}")
                }
            }
        }
    }
}

// 优化器烧录 0x04
fun executeOptimizerBurn(relayId: String, chipIdList: List<String>, chipidSeqNo: Int, moreChipid: Int, callback: (String) -> Unit) {
    if (chipIdList.isEmpty()) {
        callback("没有可烧录的优化器")
        return
    }

    // 计算byteArray的大小
    // cmd(1) + relay_id(5) + chipid_num(1) + chipid_seq_no(1) + more_chipid(1) + pchipids(5*n)
    val chipIdSize = 5 // 每个chipId是5字节
    val headerSize = 1 + chipIdSize + 1 + 1 + 1
    val totalSize = headerSize + (chipIdSize * chipIdList.size)

    val byteArray = ByteArray(totalSize)
    var offset = 0

    // cmd
    byteArray[offset++] = 0x04.toByte()

    // relay_id
    KtorSocketUtils.hexStringToBytes(relayId)?.copyInto(byteArray, offset, 0, chipIdSize)
    offset += chipIdSize

    // chipid_num
    byteArray[offset++] = chipIdList.size.toByte()

    // chipid_seq_no
    byteArray[offset++] = chipidSeqNo.toByte()

    // more_chipid
    byteArray[offset++] = moreChipid.toByte()

    // pchipids
    chipIdList.forEach { chipId ->
        if (chipId.isNotEmpty()) {
            KtorSocketUtils.convertChipId(chipId)?.copyInto(byteArray, offset, 0, chipIdSize)
            offset += chipIdSize
        }
    }

    KtorSocketUtils.sendSocketCommand(byteArray) { result ->
        when (result) {
            is KtorSocketUtils.SocketResult.Success -> callback("优化器烧录：\n${result.response}")
            is KtorSocketUtils.SocketResult.Error -> {
                CoroutineScope(Dispatchers.Main).launch {
                    callback("优化器烧录失败：${result.message}")
                }
            }
        }
    }
}

// 手动组网 0x05
fun executeManualNetworking(relayId: String, callback: (String) -> Unit) {
    val byteArray = ByteArray(6) // cmd(1) + relay_id(5)

    // cmd
    byteArray[0] = 0x05.toByte()

    // relay_id
    KtorSocketUtils.hexStringToBytes(relayId)?.copyInto(byteArray, 1, 0, 5)

    KtorSocketUtils.sendSocketCommand(byteArray) { result ->
        when (result) {
            is KtorSocketUtils.SocketResult.Success -> callback("手动组网：\n${result.response}")
            is KtorSocketUtils.SocketResult.Error -> {
                CoroutineScope(Dispatchers.Main).launch {
                    callback("手动组网失败：${result.message}")
                }
            }
        }
    }
}