package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.updateTransition
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.IconButton
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ymx.photovoltaic.ui.page.theme.LjRed
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.all
import photovoltaic_kmp_app.composeapp.generated.resources.day
import photovoltaic_kmp_app.composeapp.generated.resources.ic_set
import photovoltaic_kmp_app.composeapp.generated.resources.month
import photovoltaic_kmp_app.composeapp.generated.resources.red_add
import photovoltaic_kmp_app.composeapp.generated.resources.red_scan
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_count_abnormal
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_count_normal
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_count_offline
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_count_total
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_refresh
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_reset
import photovoltaic_kmp_app.composeapp.generated.resources.status_abnormal
import photovoltaic_kmp_app.composeapp.generated.resources.status_normal
import photovoltaic_kmp_app.composeapp.generated.resources.status_offline
import photovoltaic_kmp_app.composeapp.generated.resources.total
import photovoltaic_kmp_app.composeapp.generated.resources.year


@Composable
fun TabMenu(onClicked: (String) -> Unit) {
    val options = listOf(stringResource(Res.string.day),
        stringResource(Res.string.month),stringResource(Res.string.year),
        stringResource(Res.string.all))
    var selectedOption by remember { mutableStateOf(options[0]) }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color(0xFFF5F5F5), RoundedCornerShape(24.dp)),
        horizontalArrangement = Arrangement.SpaceEvenly,
        verticalAlignment = Alignment.CenterVertically
    ) {
        options.forEach { option ->
            Text(
                text = option,
                fontSize = 16.sp,
                color = if (option == selectedOption) LjRed else Color.Gray,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .padding(8.dp)
                    .weight(1f)
                    .background(
                        color = if (option == selectedOption) Color.White else Color.Transparent,
                        shape = RoundedCornerShape(16.dp)
                    )
                    .clickable {
                        onClicked(option)
                        selectedOption = option
                    }
                    .padding(vertical = 5.dp)
            )
        }
    }
}



data class TabItem(
    val count: Int,
    val label: String,
    val icon: DrawableResource
)

@Composable
fun FloatingStatusTab(normalCount:Int, abnormalCount:Int, offlineCount:Int) {
    val items = listOf(
        TabItem(normalCount+abnormalCount+offlineCount, stringResource(Res.string.total), Res.drawable.station_view_count_total ),
        TabItem(normalCount, stringResource(Res.string.status_normal), Res.drawable.station_view_count_normal),
        TabItem(abnormalCount, stringResource(Res.string.status_abnormal), Res.drawable.station_view_count_abnormal),
        TabItem(offlineCount, stringResource(Res.string.status_offline), Res.drawable.station_view_count_offline)
    )

    Card(
        modifier = Modifier
            .padding(start = 15.dp, end = 15.dp)
            .fillMaxWidth()
            .wrapContentHeight(),
        shape = RoundedCornerShape(16.dp),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.White)
                .padding(top = 8.dp, bottom = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            items.forEachIndexed { index, item ->
                
                if (index == 0) {
                    StatusItem(item)

                    VerticalDivider(
                        modifier = Modifier.height(40.dp),
                        thickness = 1.dp,
                        color = Color.LightGray
                    )
                }else
                {
                    StatusItem(item)
                }

            }
        }
    }
}

@Composable
private fun StatusItem(
    item: TabItem,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.wrapContentSize(),
        horizontalAlignment = Alignment.Start,
        verticalArrangement = Arrangement.Center
    ) {
        Row(
            modifier = Modifier.height(24.dp).wrapContentSize(),
            horizontalArrangement = Arrangement.Start,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(item.icon),
                contentDescription = item.label,
                modifier = Modifier.size(30.dp)
            )
        }

        Row(
            verticalAlignment = Alignment.CenterVertically 
        ){
            Text(
                text = item.label,
                modifier = Modifier.width(30.dp),
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = Color.Gray,
                    fontSize = 14.sp
                )
            )
            Text(
                text = item.count.toString(),
                style = MaterialTheme.typography.titleMedium.copy(
                    fontSize = 20.sp
                ),
                color = Color.Black,
                modifier = Modifier.padding(start = 7.dp),
                textAlign = TextAlign.Start
            )
        }
    }
}

@Composable
fun FloatingButton(onAddClicked: () -> Unit, onScanClicked: (() -> Unit)? = null) {

    Card(
        modifier = Modifier.shadow(
            elevation = 8.dp,
            shape = CircleShape,
            spotColor = Color.Black.copy(alpha = 0.25f),
            ambientColor = Color.Black.copy(alpha = 0.25f)
        ).wrapContentSize().border(
            width = 0.2.dp,
            color = Color.Black.copy(alpha = 0.1f),
            shape = CircleShape
        ),
        shape = RoundedCornerShape(50.dp),
        colors = CardDefaults.cardColors(Color.White)
    ) {
        Column(
            modifier = Modifier.padding(horizontal = 1.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            IconButton(
                onClick = {
                    onAddClicked()
                }
            ) {
                Image(
                    painter = painterResource(Res.drawable.red_add),
                    modifier = Modifier.size(22.dp),
                    contentDescription = "Search Icon",
                )
            }

            if (onScanClicked != null) {
                HorizontalDivider(
                    modifier = Modifier
                        .width(24.dp)
                        .padding(vertical = 1.dp),
                    color = Color.LightGray
                )

                IconButton(
                    onClick = {
                        onScanClicked()
                    }
                ) {
                    Image(
                        painter = painterResource(Res.drawable.red_scan),
                        modifier = Modifier.size(20.dp),
                        contentDescription = "Search Icon",
                    )
                }
            }
            }
        }
    }


@Composable
fun ExpandableFloatingActionButton(onClicked: (Int) -> Unit, selectedIconIndex: Int) {
    var isExpanded by remember { mutableStateOf(false) }


    val icons = listOf(
        Res.drawable.station_view_refresh,
        Res.drawable.station_view_reset,
//        Res.drawable.open_station,
//        Res.drawable.close_station,  // Pi symbol alternative
        Res.drawable.ic_set      // Users icon alternative
    )

    val transition = updateTransition(targetState = isExpanded, label = "expandTransition")
    val arrowRotation by transition.animateFloat(label = "arrowRotation") { expanded ->
        if (expanded) 180f else 0f
    }

        Card(
            modifier = Modifier.wrapContentSize(),
            shape = RoundedCornerShape(50.dp),
            colors = CardDefaults.cardColors(Color.White)
        ) {
            Column(
                modifier = Modifier.padding(horizontal = 1.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                val visibleIcons = if (isExpanded) icons else icons.take(4)
                visibleIcons.forEachIndexed { index, icon ->
                    if (index > 0) {
                        HorizontalDivider(
                            modifier = Modifier
                                .width(24.dp)
                                .padding(vertical = 1.dp),
                            color = Color.LightGray
                        )
                    }
                    IconButton(
                        onClick = {
                            onClicked(index)
                        }
                    ) {
                        val isSelected = index == selectedIconIndex
                        Image(
                            painter = painterResource(icon) ,
                            contentDescription = null,
                            colorFilter =  ColorFilter.tint(LocalContentColor.current),
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }

//                IconButton(onClick = { isExpanded = !isExpanded }) {
//                    Icon(
//                        imageVector = Icons.Default.KeyboardArrowDown,
//                        contentDescription = if (isExpanded) "Collapse" else "Expand",
//                        modifier = Modifier.rotate(arrowRotation)
//                    )
//                }
            }
        }
}
