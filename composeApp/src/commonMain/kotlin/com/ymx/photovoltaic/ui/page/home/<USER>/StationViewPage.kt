package com.ymx.photovoltaic.ui.page.home.station

import androidx.compose.animation.core.Animatable
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.absoluteOffset
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Card
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.data.bean.Inverter
import com.ymx.photovoltaic.data.bean.StationView
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.platform.ToastUtil
import com.ymx.photovoltaic.ui.page.common.CommonBottomBar
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.theme.LjRed
import com.ymx.photovoltaic.ui.page.widget.CustomDialog
import com.ymx.photovoltaic.ui.page.widget.CustomDropdownMenu
import com.ymx.photovoltaic.ui.page.widget.DeviceDetailDialog
import com.ymx.photovoltaic.ui.page.widget.DeviceNode
import com.ymx.photovoltaic.ui.page.widget.DeviceNodeData
import com.ymx.photovoltaic.ui.page.widget.ExpandableFloatingActionButton
import com.ymx.photovoltaic.ui.page.widget.FloatingStatusTab
import com.ymx.photovoltaic.ui.page.widget.MenuItem
import com.ymx.photovoltaic.ui.page.widget.MenuTopBar
import com.ymx.photovoltaic.ui.page.widget.OrgTreeScreen
import com.ymx.photovoltaic.ui.page.widget.SolarPanelItem
import com.ymx.photovoltaic.ui.page.widget.rememberCustomDatePickerState
import com.ymx.photovoltaic.util.CommonUtil.roundTo2Decimals
import com.ymx.photovoltaic.util.DateTimeUtil
import com.ymx.photovoltaic.viewmodel.StationViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.component_generation_percent
import photovoltaic_kmp_app.composeapp.generated.resources.component_mos_temperature
import photovoltaic_kmp_app.composeapp.generated.resources.component_power_percent
import photovoltaic_kmp_app.composeapp.generated.resources.component_temperature
import photovoltaic_kmp_app.composeapp.generated.resources.component_voltage
import photovoltaic_kmp_app.composeapp.generated.resources.confirm_title_str
import photovoltaic_kmp_app.composeapp.generated.resources.confirm_turn_off
import photovoltaic_kmp_app.composeapp.generated.resources.confirm_turn_off_groups
import photovoltaic_kmp_app.composeapp.generated.resources.confirm_turn_on_groups
import photovoltaic_kmp_app.composeapp.generated.resources.confirm_turn_on_optimizer
import photovoltaic_kmp_app.composeapp.generated.resources.continue_turn_off
import photovoltaic_kmp_app.composeapp.generated.resources.continue_turn_on
import photovoltaic_kmp_app.composeapp.generated.resources.conventional_string
import photovoltaic_kmp_app.composeapp.generated.resources.logical_view
import photovoltaic_kmp_app.composeapp.generated.resources.one_key_turn_off
import photovoltaic_kmp_app.composeapp.generated.resources.one_key_turn_on
import photovoltaic_kmp_app.composeapp.generated.resources.operation_failed
import photovoltaic_kmp_app.composeapp.generated.resources.operation_success_refresh_later
import photovoltaic_kmp_app.composeapp.generated.resources.physical_view
import photovoltaic_kmp_app.composeapp.generated.resources.refresh_component_data
import photovoltaic_kmp_app.composeapp.generated.resources.reset_component_position
import photovoltaic_kmp_app.composeapp.generated.resources.smart_string
import photovoltaic_kmp_app.composeapp.generated.resources.station_view
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_abnormal
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_logic
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_normal_100
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_normal_20
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_normal_40
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_normal_60
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_normal_80
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_offline
import photovoltaic_kmp_app.composeapp.generated.resources.station_view_physics
import photovoltaic_kmp_app.composeapp.generated.resources.status_closed
import photovoltaic_kmp_app.composeapp.generated.resources.status_normal
import photovoltaic_kmp_app.composeapp.generated.resources.status_off
import photovoltaic_kmp_app.composeapp.generated.resources.status_warning
import photovoltaic_kmp_app.composeapp.generated.resources.turn_off_groups
import photovoltaic_kmp_app.composeapp.generated.resources.turn_off_station
import photovoltaic_kmp_app.composeapp.generated.resources.turn_off_station_warning
import photovoltaic_kmp_app.composeapp.generated.resources.turn_off_station_warning_second
import photovoltaic_kmp_app.composeapp.generated.resources.turn_on_groups
import photovoltaic_kmp_app.composeapp.generated.resources.turn_on_station
import photovoltaic_kmp_app.composeapp.generated.resources.turn_on_station_warning
import photovoltaic_kmp_app.composeapp.generated.resources.turn_on_station_warning_second
import photovoltaic_kmp_app.composeapp.generated.resources.warning_title_str


@Composable
fun StationViewPage(
    navHostController: NavHostController,
    stationViewModel: StationViewModel = getKoin().get()
) {

    val stationView by stationViewModel.stationViewFlow.collectAsState()

    val optimizerList = stationView?.data

    val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
    val currentTimeOnlyMinutes = "${now.year}-${now.monthNumber.toString().padStart(2, '0')}-${now.dayOfMonth.toString().padStart(2, '0')} ${now.hour.toString().padStart(2, '0')}:${now.minute.toString().padStart(2, '0')}"

    // 在 Composable 函数顶层获取字符串资源
    val turnOffGroupsTitle = stringResource(Res.string.turn_off_groups)
    val turnOnGroupsTitle = stringResource(Res.string.turn_on_groups)
    val resetComponentPositionStr = stringResource(Res.string.reset_component_position)
    val operationFailedStr = stringResource(Res.string.operation_failed)
    val operationSuccessRefreshLaterStr = stringResource(Res.string.operation_success_refresh_later)
    val refreshComponentData = stringResource(Res.string.refresh_component_data)

    // 添加日期选择器状态
    val datePickerState = rememberCustomDatePickerState()
    var selectedDate by remember { mutableStateOf<LocalDate?>(null) }

    val startDate = if (AppGlobal.powCreateTime > 0) {
        DateTimeUtil.fromEpochMillis(AppGlobal.powCreateTime)
    } else {
        // 如果没有有效的创建时间，使用一年前作为默认开始日期
        DateTimeUtil.minusYears(DateTimeUtil.now(), 1)
    }



    // 添加定时器控制状态
    var isTimerEnabled by remember { mutableStateOf(true) }

    // 保存实时模式下的优化器选项
    var realTimeOptimizerOptions by remember { mutableStateOf<List<String>?>(null) }

    // 创建一个函数来格式化LocalDate为"yyyy-MM-dd HH:mm"格式
    fun formatDateTimeForApi(date: LocalDate): String {
        return "${date.year}-${date.monthNumber.toString().padStart(2, '0')}-${date.dayOfMonth.toString().padStart(2, '0')}"
    }

    // 获取当前使用的时间字符串
    val currentTimeForApi = selectedDate?.let { formatDateTimeForApi(it) } ?: currentTimeOnlyMinutes

    // 启动协程，每隔20秒执行一次（只有在定时器启用时）
    LaunchedEffect(isTimerEnabled, currentTimeForApi) {
        if (isTimerEnabled) {
            while (isTimerEnabled) {
                // type为1 设置时间轴信息 不为1 不设置 from为view返回电气信息 不为view只返回位置和告警信息
                // groupId 有值则只查询该组串的信息 date传值之前日期，则查询的是历史数据
                // status 1 打开 2 关闭 3 有告警  是否告警的信息已返回
                stationViewModel.fetchComponentList(
                    AppGlobal.powerStationId,
                    currentTimeForApi,
                    "1",
                    AppGlobal.sunUpTime,
                    AppGlobal.sunDownTime,
                    "view",
                    null
                )
                delay(20000) // 20秒延迟
            }
        }
    }


    // 状态提升，把子方法中的状态提升到父方法中，供其他方法使用  子方法传递不变量或者方法
    var selectedIconIndex by remember { mutableIntStateOf(-1) }

    // 检查是否有有效的MOS管温度数据（不为-130的值）
    val hasMosTemperatureData = optimizerList?.any { it.mosTemperature != -130 } ?: false

    // 检查是否有有效的累计发电量数据（不为-1.0的值）
    val hasKwhData = optimizerList?.any { it.kwh != -1.0 } ?: false

    // 获取字符串资源
    val powerPercentStr = stringResource(Res.string.component_power_percent)
    val generationPercentStr = stringResource(Res.string.component_generation_percent)
    val temperatureStr = stringResource(Res.string.component_temperature)
    val mosTemperatureStr = stringResource(Res.string.component_mos_temperature)
    val voltageStr = stringResource(Res.string.component_voltage)

    // 计算当前的优化器选项（基于当前数据）
    val currentOptimizerOptions = remember(hasMosTemperatureData, hasKwhData, powerPercentStr, generationPercentStr, temperatureStr, mosTemperatureStr, voltageStr) {
        buildList {
            add(powerPercentStr)
            if (hasKwhData) {
                add(generationPercentStr)
            }
            add(temperatureStr)
            if (hasMosTemperatureData) {
                add(mosTemperatureStr)
            }
            add(voltageStr)
        }
    }

    // 在实时模式下保存选项，在历史模式下使用保存的选项
    val optimizerOptions = if (selectedDate == null) {
        // 实时模式：使用当前计算的选项并保存
        realTimeOptimizerOptions = currentOptimizerOptions
        currentOptimizerOptions
    } else {
        // 历史模式：使用保存的实时选项，如果没有则使用当前选项
        realTimeOptimizerOptions ?: currentOptimizerOptions
    }
    var selectedOptimizerOption by remember { mutableStateOf(optimizerOptions[0]) }

    // 当选项不可用时，如果当前选中的选项不在列表中，则重置为第一个选项
    LaunchedEffect(optimizerOptions) {
        if (!optimizerOptions.contains(selectedOptimizerOption)) {
            selectedOptimizerOption = optimizerOptions[0]
        }
    }

    var trigger by remember { mutableStateOf(0) }

    // 创建专门的手势状态管理
    @Stable
    class GestureState(initialScale: Float, initialOffsetX: Float, initialOffsetY: Float) {
        var scale by mutableFloatStateOf(initialScale)
        var offsetX by mutableFloatStateOf(initialOffsetX)
        var offsetY by mutableFloatStateOf(initialOffsetY)
        
        private var lastUpdateTime = 0L
        private val updateThreshold = 16L // 约60fps
        
        fun updateTransform(newScale: Float, newOffsetX: Float, newOffsetY: Float) {
            val currentTime = Clock.System.now().toEpochMilliseconds()
            if (currentTime - lastUpdateTime > updateThreshold) {
                scale = newScale
                offsetX = newOffsetX
                offsetY = newOffsetY
                lastUpdateTime = currentTime
            }
        }
        
        fun reset(resetScale: Float, resetOffsetX: Float, resetOffsetY: Float) {
            scale = resetScale
            offsetX = resetOffsetX
            offsetY = resetOffsetY
        }
    }
    
    val gestureState = remember { GestureState(0.8f, -260f, 0f) }
    val treeGestureState = remember { GestureState(0.8f, -200f, 0f) }
    
    // 使用 Animatable 进行平滑动画
    val scaleAnimatable = remember { Animatable(0.8f) }
    val offsetXAnimatable = remember { Animatable(-260f) }
    val offsetYAnimatable = remember { Animatable(0f) }
    
    val treeScaleAnimatable = remember { Animatable(0.8f) }
    val treeOffsetXAnimatable = remember { Animatable(-200f) }
    val treeOffsetYAnimatable = remember { Animatable(0f) }
    
    // 在手势处理中使用 snapTo 而不是直接修改状态
    LaunchedEffect(gestureState.scale, gestureState.offsetX, gestureState.offsetY) {
        launch { scaleAnimatable.snapTo(gestureState.scale) }
        launch { offsetXAnimatable.snapTo(gestureState.offsetX) }
        launch { offsetYAnimatable.snapTo(gestureState.offsetY) }
    }
    
    LaunchedEffect(treeGestureState.scale, treeGestureState.offsetX, treeGestureState.offsetY) {
        launch { treeScaleAnimatable.snapTo(treeGestureState.scale) }
        launch { treeOffsetXAnimatable.snapTo(treeGestureState.offsetX) }
        launch { treeOffsetYAnimatable.snapTo(treeGestureState.offsetY) }
    }


    var selectedTab by remember { mutableIntStateOf(0) }

    // 添加一个状态来控制 CustomDialog 的显示
    var showOperationDialog by remember { mutableStateOf(false) }
    var showSecondOperationDialog by remember { mutableStateOf(false) }

    LaunchedEffect(selectedIconIndex, trigger) {
        when (selectedIconIndex) {
            0 -> {
                ToastUtil.showShort(refreshComponentData)
                // 重新启动定时器
                isTimerEnabled = true
                selectedDate = null // 清除选择的日期，回到实时模式
                // 刷新数据
                delay(300)
                stationViewModel.fetchComponentList(
                    AppGlobal.powerStationId,
                    currentTimeOnlyMinutes,
                    "1",
                    AppGlobal.sunUpTime,
                    AppGlobal.sunDownTime,
                    "view",
                    null
                )
            }
            1 -> {
                ToastUtil.showShort(resetComponentPositionStr)
                // 根据当前选中的Tab重置对应视图的缩放状态
                if (selectedTab == 0) {
                    // 重置物理视图(CardGrid)的缩放状态
                    gestureState.reset(0.8f, -260f, 0f)
                } else {
                    // 重置逻辑视图(OrgTree)的缩放状态
                    treeGestureState.reset(0.8f, -200f, 0f)
                }
            }
            2-> {
                // 暂停定时器并显示日期选择对话框
                isTimerEnabled = false
                val currentDate = selectedDate ?: DateTimeUtil.minusDays(DateTimeUtil.now(),1)
                datePickerState.show(
                    value = currentDate,
                    start = startDate,
                    end = DateTimeUtil.minusDays(DateTimeUtil.now(),1)
                ) { selectedLocalDate ->
                    selectedDate = selectedLocalDate
                    // 使用选择的日期调用fetchComponentList
                    stationViewModel.fetchComponentList(
                        AppGlobal.powerStationId,
                        formatDateTimeForApi(selectedLocalDate),
                        "1",
                        AppGlobal.sunUpTime,
                        AppGlobal.sunDownTime,
                        "view",
                        null
                    )
                }
            }
        }
    }

    // 将groupList转为map，方便获取组串名称
    val groupMap = stationView?.groupList?.associateBy { it.groupId }

    // 计算每个组串的状态
    val groupStatusMap = optimizerList?.groupBy { it.groupId }?.mapValues { (_, optimizers) ->
        val allStatus2 = optimizers.all { it.status == 2 }
        val hasStatus2 = optimizers.any { it.status == 2 }
        val hasStatus1or3 = optimizers.any { it.status == 1 || it.status == 3 }
        
        when {
            allStatus2 -> 2 // 全部关闭
            !hasStatus2 -> 1 // 没有关闭的，全部是开启或告警
            hasStatus2 && hasStatus1or3 -> 3 // 状态不统一，有开有关
            else -> 1 // 默认为开启
        }
    } ?: emptyMap()

    optimizerList?.forEach {
        it.groupName = groupMap?.get(it.groupId)?.groupName ?: ""
        // 设置组串状态
        it.groupStatus = groupStatusMap[it.groupId] ?: 1
        // 只有当输出电压和电流都不为-1时才计算功率
        if (it.outputVoltage != -1 && it.outputCurrent != -1) {
            it.power = (it.outputVoltage * it.outputCurrent / 1000000f).roundTo2Decimals()
        }
    }

    // 按组串分组计算最大功率
    val groupMaxPowers = optimizerList?.groupBy { it.groupId }?.mapValues { (_, optimizers) ->
        optimizers.mapNotNull { if (it.power != -1f) it.power else null }.maxOrNull() ?: 0f
    } ?: emptyMap()

    // 获取每个组串的额定功率
    val groupRatedPowers = stationView?.groupList?.associate { 
        it.groupId to (it.power.toFloat()) 
    } ?: emptyMap()

    // 计算每个优化器的功率百分比
    optimizerList?.forEach { optimizer ->
        // 计算电源对组串最大功率的百分比
        optimizer.powerRatio = if (optimizer.power == -1f) {
            ""
        } else {
            val maxPowerInGroup = groupMaxPowers[optimizer.groupId] ?: 0f
            if (maxPowerInGroup > 0 && optimizer.power >= 0) {
                "${(optimizer.power / maxPowerInGroup * 100).roundTo2Decimals()}%"
            } else {
                "0.0%"
            }
        }
        
        // 计算功率对组串额定功率的百分比
        val ratedPower = groupRatedPowers[optimizer.groupId] ?: 0f
        if (optimizer.power != -1f && ratedPower > 0) {
            optimizer.ratedPowerRatio = (optimizer.power / ratedPower * 100f).coerceIn(0f, 100f)
        } else {
            optimizer.ratedPowerRatio = 0f
        }
    }

    val normalCount = optimizerList?.count { it.status == 1 } ?: 0
    val offlineCount = optimizerList?.count { it.status == 2 } ?: 0
    val abnormalCount = optimizerList?.count { it.status == 3 } ?: 0

    Scaffold(
        topBar = {
            Column() {
                MenuTopBar(
                    textStr = stringResource(Res.string.station_view),
                    backClick = { navHostController.navigate(Route.HOME) },
                    showMenuButton = true,
                    menuItems = listOf(
                        MenuItem(stringResource(Res.string.one_key_turn_on), {
                            // 设置selectedIconIndex为2（打开）并触发对话框
                            selectedIconIndex = 2
                            showOperationDialog = true
                        }),
                        MenuItem(stringResource(Res.string.one_key_turn_off), {
                            // 设置selectedIconIndex为3（关闭）并触发对话框
                            selectedIconIndex = 3
                            showOperationDialog = true
                        }, Color.Red)
                    )
                )

                TabRow(
                    selectedTabIndex = selectedTab,
                    modifier = Modifier.background(Color.White),
                    // Remove the indicator to hide the colored line at the bottom of selected tab
                    indicator = { },
                    // Center the tabs by setting divider to null and using equal weights
                    divider = { }
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color.White),
                        horizontalArrangement = Arrangement.Center
                    ) {
                        // First tab
                        Tab(
                            selected = selectedTab == 0,
                            onClick = { selectedTab = 0 },
                            text = {
                                Row(verticalAlignment = Alignment.CenterVertically) {
                                    Icon(
                                        painter = painterResource(Res.drawable.station_view_physics),
                                        contentDescription = null,
                                        tint = if (selectedTab == 0) LjRed else Color(0xFF8A8A8A),
                                        modifier = Modifier.size(16.dp)
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(
                                        text = stringResource(Res.string.physical_view),
                                        color = if (selectedTab == 0) LjRed else Color(0xFF8A8A8A)
                                    )
                                }
                            },
                            modifier = Modifier
                                .background(Color.White)
                                .weight(1f)
                        )

                        // Divider between tabs - smaller height and centered
                        VerticalDivider(
                            modifier = Modifier
                                .fillMaxHeight(0.3f)
                                .background(Color.White)
                                .align(Alignment.CenterVertically),
                            thickness = 1.dp,
                            color = Color.LightGray
                        )

                        // Second tab
                        Tab(
                            selected = selectedTab == 1,
                            onClick = { selectedTab = 1 },
                            text = {
                                Row(verticalAlignment = Alignment.CenterVertically) {
                                    Icon(
                                        painter = painterResource(Res.drawable.station_view_logic),
                                        contentDescription = null,
                                        tint = if (selectedTab == 1) LjRed else Color(0xFF8A8A8A),
                                        modifier = Modifier.size(16.dp)
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(
                                        text = stringResource(Res.string.logical_view),
                                        color = if (selectedTab == 1) LjRed else Color(0xFF8A8A8A)
                                    )
                                }
                            },
                            modifier = Modifier
                                .background(Color.White)
                                .weight(1f)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(10.dp))

                FloatingStatusTab(normalCount, abnormalCount, offlineCount)

                // 组件模式 组串模式选项卡
//                TabSection(selectedTab) { selectedTab = it }
                // 下拉框
                Card(
                    modifier = Modifier
                        .background(Grey_F5)
                        .fillMaxWidth()
                        .padding(top = 10.dp, start = 10.dp, end = 10.dp)
                )
                {
                    Row(
                        modifier = Modifier
                            .background(Grey_F5)
                            .fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {

                        // 下拉菜单
                        CustomDropdownMenu(
                            optimizerOptions, selectedOptimizerOption
                        ) {
                            selectedOptimizerOption = it
                        }

                    }
                }


            }
        },
        containerColor = Grey_F5,
        floatingActionButton = {
            ExpandableFloatingActionButton(
                onClicked = { index ->
                    run {
                        selectedIconIndex = index

                        if (selectedIconIndex == 0 || selectedIconIndex == 1|| selectedIconIndex == 2) {
                            trigger++
                        }
                    }
                },
                selectedIconIndex = selectedIconIndex,
            )
        },
        bottomBar = {
            CommonBottomBar(navController = navHostController, isFirstMenu = false)
        }
    ) {

            paddingValues ->
        Column(
            modifier = Modifier
                .padding(paddingValues)
                .padding(start = 10.dp, end = 10.dp)
        ) {

            // 显示日期选择器（当选择了历史日期时）
            selectedDate?.let { date ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    horizontalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = date.toString(),
                        color = Color.Black
                    )
                }
            }

            var isHorizontalExpanded by remember { mutableStateOf(true) }
            var isVerticalExpanded by remember { mutableStateOf(true) }
            var thumbnailOffset by remember { mutableStateOf(0f) }


            Box(modifier = Modifier.weight(1f)) {
                // Only show CardGrid in physical view
                if (selectedTab == 0) {
                    Column {
                        if (optimizerList != null) {
                            CardGrid(
                                scaleAnimatable.value,
                                offsetXAnimatable.value,
                                offsetYAnimatable.value,
                                optimizerList,
                                selectedIconIndex,
                                stationViewModel,
                                selectedOptimizerOption
                            ) { centroid, pan, zoom, rotation ->
                                // 修改缩放逻辑，确保即使在最小缩放值时也能放大
                                // zoom > 1表示放大，zoom < 1表示缩小
                                val newScale = if (zoom > 1.0f || gestureState.scale > 0.6f) {
                                    // 放大操作或者当前缩放值大于最小值，正常计算
                                    (gestureState.scale * zoom).coerceIn(0.6f, 1f)
                                } else {
                                    // 缩小操作且当前已经是最小值，保持不变
                                    gestureState.scale
                                }
                                val newOffsetX = gestureState.offsetX + pan.x
                                val newOffsetY = gestureState.offsetY + pan.y
                                gestureState.updateTransform(newScale, newOffsetX, newOffsetY)
                            }
                        }
                    }
                } else {
                    // 将原来的deviceTreeData创建逻辑替换为方法调用
                    val deviceTreeData = remember(stationView) {
                        createDeviceTreeData(stationView)
                    }

                    // 使用构建好的设备树数据
                    if (selectedTab == 1) {
                        OrgTreeScreen(
                            dataSource = deviceTreeData,
                            scale = treeScaleAnimatable.value,
                            offsetX = treeOffsetXAnimatable.value,
                            offsetY = treeOffsetYAnimatable.value,
                            stationViewModel,
                            selectedOptimizerOption
                        ){ centroid, pan, zoom, rotation ->
                            val newScale = if (zoom > 1.0f || treeGestureState.scale > 0.6f) {
                                // 放大操作或者当前缩放值大于最小值，正常计算
                                (treeGestureState.scale * zoom).coerceIn(0.6f, 1f)
                            } else {
                                // 缩小操作且当前已经是最小值，保持不变
                                treeGestureState.scale
                            }
                            val newOffsetX = treeGestureState.offsetX + pan.x
                            val newOffsetY = treeGestureState.offsetY + pan.y
                            treeGestureState.updateTransform(newScale, newOffsetX, newOffsetY)
                        }
                    }
                }
            }

        }
    }

    // 添加 CustomDialog
    if (showOperationDialog) {
        val isTurnOn = selectedIconIndex == 2
        val confirmStr = if (isTurnOn) 
            stringResource(Res.string.turn_on_station) 
        else 
            stringResource(Res.string.turn_off_station)
        val contextStr = if (isTurnOn)
            stringResource(Res.string.turn_on_station_warning)
        else
            stringResource(Res.string.turn_off_station_warning)
        val confirmButtonText = if (isTurnOn)
            stringResource(Res.string.continue_turn_on)
        else
            stringResource(Res.string.continue_turn_off)
            
        CustomDialog(
            confirmStr = confirmStr,
            contextStr = contextStr,
            confirmButtonText = confirmButtonText,
            onConfirm = {
                showOperationDialog = false
                // 显示第二个确认对话框
                showSecondOperationDialog = true
            },
            onCancel = {
                showOperationDialog = false
            }
        )
    }
    
    if (showSecondOperationDialog) {
        val isTurnOn = selectedIconIndex == 2
        val confirmStr = if (isTurnOn) 
            stringResource(Res.string.warning_title_str) 
        else 
            stringResource(Res.string.confirm_title_str)
        val contextStr = if (isTurnOn)
            stringResource(Res.string.turn_on_station_warning_second)
        else
            stringResource(Res.string.turn_off_station_warning_second)
        val confirmButtonText = if (isTurnOn)
            stringResource(Res.string.continue_turn_on)
        else
            stringResource(Res.string.continue_turn_off)
            
        CustomDialog(
            confirmStr = confirmStr,
            contextStr = contextStr,
            confirmButtonText = confirmButtonText,
            onConfirm = {
                showSecondOperationDialog = false
                
                // 2是打开，3是关闭，对应flag 2是打开，1是关闭
                val flag = if (isTurnOn) 2 else 1
                
                stationViewModel.remoteControllerAck(
                    powerStationId = AppGlobal.powerStationId,
                    groupId = null,
                    chipId = null,
                    flag = flag,
                    errorBlock = {
                        ToastUtil.showLong(operationFailedStr)
                    }
                ) {
                    ToastUtil.showLong(operationSuccessRefreshLaterStr)
                }
            },
            onCancel = {
                showSecondOperationDialog = false
            }
        )
    }
}


@Composable
fun CardGrid(
    scale: Float,
    offsetX: Float,
    offsetY: Float,
    optimizerList: List<StationView.InnerOptimizer>,
    selectIconIndex: Int,
    stationViewModel: StationViewModel,
    selectedOptimizerOption: String,
    onTransformGesture: (centroid: Offset, pan: Offset, zoom: Float, rotation: Float) -> Unit
) {
    var selectedComponent by remember { mutableStateOf<StationView.InnerOptimizer?>(null) }
    var showGroupBorders by remember { mutableStateOf(false) }
    var showTurnOffDialog by remember { mutableStateOf(false) }
    var smartSwitchEnabled by remember { mutableStateOf(false) }


    // 点击第一个按钮，显示组串边框 怎么保证相邻的组串颜色不同
//    if (selectIconIndex == 0)
//        showGroupBorders = true
//    else showGroupBorders = false


    // 优化器显示区域
    // 计算最大的gapLeft和gapTop
    val maxGapLeft = optimizerList.maxOfOrNull { it.gapLeft.toIntOrNull() ?: 0 } ?: 0
    val maxGapTop = optimizerList.maxOfOrNull { it.gapTop.toIntOrNull() ?: 0 } ?: 0
    
    // 按照选中代码的计算方式设置Box大小，加上组件本身的大小(60dp宽，90dp高)，并乘以1.5系数
    val boxWidth = ((maxGapLeft * 9 + 60)).dp
    val boxHeight = ((maxGapTop * 10 + 90)).dp
    
    // 打印boxWidth和boxHeight的值
//    println("boxWidth: $boxWidth, boxHeight: $boxHeight")
    
    Box(
        modifier = Modifier.size(boxWidth, boxHeight)
            .graphicsLayer(
                scaleX = scale,
                scaleY = scale,
                translationX = offsetX,
                translationY = offsetY
            )
            .pointerInput(Unit) {
                var lastUpdateTime = 0L
                val updateInterval = 16L // 约60fps，减少更新频率
                
                detectTransformGestures { centroid, pan, zoom, rotation ->
                    val currentTime = Clock.System.now().toEpochMilliseconds()
                    if (currentTime - lastUpdateTime >= updateInterval) {
                        onTransformGesture(centroid, pan, zoom, rotation)
                        lastUpdateTime = currentTime
                    }
                }
            }


    ) {
        // 如果是智能光伏演示电站，则显示两个组串名称
        if (AppGlobal.powerStationId == "1941AD070B5083216C31FCC31B13443B") {
            Text(
                text = stringResource(Res.string.conventional_string),
                color = Color.White,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .width((45 * 9).dp)
                    .height(26.dp)
                    .absoluteOffset(x = (9 * 8).dp, y = (7 * 8).dp)
                    .background(Color.Blue)
            )

            Text(
                text = stringResource(Res.string.smart_string),
                color = Color.White,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .width((45 * 9).dp)
                    .height(26.dp)
                    .absoluteOffset(x = (9 * 8).dp, y = (42 * 8).dp)
                    .background(Color.Blue)
            )
        }

        optimizerList.forEach { item ->
            val isSelected = selectedComponent?.chipId == item.chipId

            val displayValue = calculateDisplayValue(
                optimizer = item,
                selectedOptimizerOption = selectedOptimizerOption
            )

            SolarPanelItem(
                power = if (item.status == 2) "" else if (item.power != -1f) "${item.power}W" else "",
                voltage = if (item.status == 2) stringResource(Res.string.status_closed) else displayValue,
                id = if(item.chipId.length>6) item.chipId.substring(2,7) else item.chipId,
                backgroundImage = when (item.status) {
                    1 -> {
                        // 使用额定功率比例来判断
                        val powerPercent = item.ratedPowerRatio
                        
                        when {
                            powerPercent > 80f -> painterResource(Res.drawable.station_view_normal_100)
                            powerPercent > 60f -> painterResource(Res.drawable.station_view_normal_80)
                            powerPercent > 40f -> painterResource(Res.drawable.station_view_normal_60)
                            powerPercent > 20f -> painterResource(Res.drawable.station_view_normal_40)
                            else -> painterResource(Res.drawable.station_view_normal_20)
                        }
                    }
                    2 -> painterResource(Res.drawable.station_view_offline)
                    3 -> painterResource(Res.drawable.station_view_abnormal)
                    else -> painterResource(Res.drawable.station_view_normal_60)
                },
                modifier = Modifier
                    .size(60.dp, 90.dp)
                    .absoluteOffset(
                        x = (item.gapLeft.toInt()*9).dp,
                        y = (item.gapTop.toInt()*10).dp
                    )
                    .clickable { selectedComponent = item }
                    // 选中时添加一个黄色半透明覆盖层
                    .then(
                        if (isSelected) {
                            Modifier.drawBehind {
                                drawRect(Color.Yellow.copy(alpha = 0.3f))
                            }
                        }
                         else Modifier
                    ). pointerInput(Unit) {
                        var lastUpdateTime = 0L
                        val updateInterval = 16L // 约60fps，减少更新频率

                        detectTransformGestures { centroid, pan, zoom, rotation ->
                            val currentTime = Clock.System.now().toEpochMilliseconds()
                            if (currentTime - lastUpdateTime >= updateInterval) {
                                onTransformGesture(centroid, pan, zoom, rotation)
                                lastUpdateTime = currentTime
                            }
                        }
                    }
            )
        }


        // 替换为DeviceDetailDialog
        if (selectedComponent != null) {
            val statusText = when(selectedComponent!!.status) {
                1 -> stringResource(Res.string.status_normal)
                2 -> stringResource(Res.string.status_off)
                3 -> stringResource(Res.string.status_warning)
                else -> stringResource(Res.string.status_normal)
            }
            
            val powerText = if (selectedComponent!!.power == -1f) "" else "${selectedComponent!!.power} W"
            val voltageText = if (selectedComponent!!.outputVoltage == -1) "" else "${(selectedComponent!!.outputVoltage/1000).roundTo2Decimals()} V"
            val temperatureText = if (selectedComponent!!.componentTemperature == -130) "" else "${selectedComponent!!.componentTemperature} °C"
            val mosTemperatureText = if (selectedComponent!!.mosTemperature == -130) "" else "${selectedComponent!!.mosTemperature} °C"
            
            // 查找当前组串下的所有组件
            val groupId = selectedComponent!!.groupId
            val componentsInSameGroup = optimizerList.filter { it.groupId == groupId }
            
            // 判断组串中所有组件是否状态一致
            val hasNormal = componentsInSameGroup.any { it.status == 1 }
            val hasWarning = componentsInSameGroup.any { it.status == 3 }
            val hasClosed = componentsInSameGroup.any { it.status == 2 }
            val allSameStatus =
                !((hasNormal && hasClosed) || (hasWarning && hasClosed))
            
            // 获取组串状态 - 如果所有组件状态一致，则取第一个组件的状态
            val groupStatus = if (allSameStatus) componentsInSameGroup.first().status else -1
            
            var showGroupTurnOffDialog by remember { mutableStateOf(false) }
            
            DeviceDetailDialog(
                deviceNo = "No. ${selectedComponent!!.chipId}",
                deviceState = statusText,
                devicePower = powerText,
                outputVoltage = voltageText,
                deviceTemperature = temperatureText,
                deviceMosTemperature = mosTemperatureText,
                deviceGroup = selectedComponent!!.groupName,
                smartSwitchEnabled = selectedComponent!!.status != 2,
                onSmartSwitchChanged = {
                    showTurnOffDialog = true

                },
                // 当组串内所有设备状态一致时，显示组串开关
                showGroupSwitch = allSameStatus && componentsInSameGroup.size > 1,
                groupSwitchEnabled = groupStatus != 2,
                onGroupSwitchChanged = {
                    showGroupTurnOffDialog = true
                },
                onDismiss = {
                    selectedComponent = null
                }
            )
            
            // 组串操作确认对话框
            if (showGroupTurnOffDialog) {
                val operationFailedStr = stringResource(Res.string.operation_failed)
                val operationSuccessRefreshLaterStr = stringResource(Res.string.operation_success_refresh_later)
                val turnOffGroupsTitle = stringResource(Res.string.confirm_turn_off_groups)
                val turnOnGroupsTitle = stringResource(Res.string.confirm_turn_on_groups)
                
                val isGroupOn = groupStatus != 2
                
                CustomDialog(
                    confirmStr = if (isGroupOn) 
                        turnOffGroupsTitle.replace("%s", selectedComponent!!.groupName)
                    else 
                        turnOnGroupsTitle.replace("%s", selectedComponent!!.groupName),
                    onConfirm = {
                        // 关闭对话框
                        showGroupTurnOffDialog = false
                        
                        // 传递的操作 1是关闭 2是打开
                        val flag = if (isGroupOn) 1 else 2
                        
                        // 调用组串控制接口，电站id应该传null
                        stationViewModel.remoteControllerAck(
                            null,
                            selectedComponent!!.groupId,
                            null,
                            flag,
                            errorBlock = {
                                ToastUtil.showLong(operationFailedStr)
                            }
                        ) {
                            ToastUtil.showLong(operationSuccessRefreshLaterStr)
                            selectedComponent = null
                        }
                    },
                    onCancel = {
                        showGroupTurnOffDialog = false
                    }
                )
            }
        }

        ShowTurnOffComponentDialog(
            showDialog = showTurnOffDialog,
            selectedComponent = selectedComponent,
            stationViewModel = stationViewModel,
            onDismiss = { showTurnOffDialog = false },
            onSuccess = { selectedComponent = null }
        )
    }


}

// 修改后的非 Composable 函数
@Composable
fun calculateDisplayValue(
    optimizer: StationView.InnerOptimizer,
    selectedOptimizerOption: String,
): String {
    val voltageStr = stringResource(Res.string.component_voltage)
    val temperatureStr = stringResource(Res.string.component_temperature)
    val mosTemperatureStr = stringResource(Res.string.component_mos_temperature)
    val generationStr = stringResource(Res.string.component_generation_percent)

    return when (selectedOptimizerOption) {
        voltageStr ->
            if (optimizer.outputVoltage != -1) "${(optimizer.outputVoltage / 1000).roundTo2Decimals()}V" else ""
        temperatureStr ->
            if (optimizer.componentTemperature != -130) "${optimizer.componentTemperature}℃" else ""
        mosTemperatureStr ->
            if (optimizer.mosTemperature != -130) "${optimizer.mosTemperature}℃" else ""
        generationStr ->
            if (optimizer.kwh != -1.0) "${optimizer.kwh}度" else ""
        else -> optimizer.powerRatio
    }
}

@Composable
fun ShowTurnOffComponentDialog(
    showDialog: Boolean,
    selectedComponent: StationView.InnerOptimizer?,
    stationViewModel: StationViewModel,
    onDismiss: () -> Unit,
    onSuccess: () -> Unit
) {

    val operationFailedStr = stringResource(Res.string.operation_failed)
    val operationSuccessRefreshLaterStr = stringResource(Res.string.operation_success_refresh_later)


    if (showDialog && selectedComponent != null) {
        CustomDialog(
            confirmStr = if(selectedComponent.status==2)
                stringResource(Res.string.confirm_turn_on_optimizer).replace("%s", selectedComponent.chipId)
                        else  stringResource(Res.string.confirm_turn_off).replace("%s", selectedComponent.chipId),

                    onConfirm = {
                onDismiss()

                // 传递的是优化器的状态 1 打开 2 关闭 3 告警 对应的操作 关闭时打开 其他情况下关闭
                // 在操作命令中 1是关闭 2 打开
              val  flag = when(selectedComponent.status) {
                    2-> 2
                    else-> 1
                }

                stationViewModel.remoteControllerAck(
                    null,
                    null,
                    selectedComponent.equipmentId,
                    flag,
                    errorBlock = {
                        ToastUtil.showLong(operationFailedStr)
                    }
                ) {
                    ToastUtil.showLong(operationSuccessRefreshLaterStr)
                    onSuccess()
                }
            },
            onCancel = {
                onDismiss()
            }
        )
    }
}

@Composable
fun ThumbnailArea(onOffsetChanged: (Float) -> Unit) {
    var dragOffset by remember { mutableStateOf(0f) }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(100.dp)
            .background(Color.LightGray)
            .pointerInput(Unit) {
                detectDragGestures { change, dragAmount ->
                    change.consume()
                    dragOffset += dragAmount.y
                    onOffsetChanged(dragOffset)
                }
            }
    ) {
        // 缩略图内容
        Box(
            modifier = Modifier
                .fillMaxWidth(0.3f)
                .fillMaxHeight(0.3f)
                .background(Color.Gray)
                .align(Alignment.Center)
        )
    }
}


// 添加新的方法用于创建设备树数据
private fun createDeviceTreeData(stationView: StationView?): List<DeviceNode<DeviceNodeData>> {
    val optimizerList = stationView?.data ?: emptyList()
    val groupList = stationView?.groupList ?: emptyList()
    val collectorList = stationView?.collectorList ?: emptyList()
    
    return listOf(
        DeviceNode(
            DeviceNodeData.InverterNode(
                inverter = Inverter(
                    id = "INV001",
                    inverterName = "逆变器1"
                )
            ),
            // 将采集器列表转换为DeviceNode
            collectorList.map { collector ->
                DeviceNode(
                    DeviceNodeData.CollectorNode(
                        collector = collector
                    ),
                    // 将组串列表转换为DeviceNode
                    groupList.map { group ->
                        DeviceNode(
                            DeviceNodeData.GroupNode(
                                group = group
                            ),
                            // 找出属于该组串的所有优化器，作为子节点
                            optimizerList
                                .filter { it.groupId == group.groupId }
                                .map { optimizer ->
                                    DeviceNode(
                                        DeviceNodeData.OptimizerNode(
                                            optimizer = optimizer
                                        )
                                    )
                                }
                        )
                    }
                )
            }
        )
    )
}